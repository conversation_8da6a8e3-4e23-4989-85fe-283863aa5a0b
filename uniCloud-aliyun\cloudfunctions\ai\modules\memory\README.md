# AI记忆功能模块

## 概述

AI记忆功能模块为AI智能任务系统提供用户个性化记忆存储和管理能力。通过此模块，AI可以记住用户的偏好、习惯和重要信息，从而提供更加个性化的服务。

## 功能特性

- ✅ 记忆创建：保存用户想要AI记住的重要信息
- ✅ 记忆查询：获取用户之前保存的记忆信息
- ✅ 记忆更新：修改已保存的记忆内容
- ✅ 记忆删除：删除不再需要的记忆
- ✅ 权限控制：每个用户只能访问自己的记忆
- ✅ 数量限制：每用户最多100条记忆，防止滥用
- ✅ 内容限制：每条记忆最多2000字符
- ✅ Function Calling集成：支持AI自动调用记忆功能

## 模块结构

```
modules/memory/
├── config.js      # 配置文件：错误码、常量、响应模板
├── utils.js       # 工具函数：响应构建、参数校验、时间格式化
├── memories.js    # 核心类：MemoryManager，实现CRUD操作
├── index.js       # 主入口：MemoryTool，统一工具执行接口
└── README.md      # 说明文档
```

## API接口

### 1. createMemory - 创建记忆

**参数：**
- `userId` (string, 必需): 用户ID
- `content` (string, 必需): 记忆内容，最多2000字符
- `startDate` (string, 可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (string, 可选): 结束日期，格式：YYYY-MM-DD

**返回：**
```json
{
  "success": true,
  "message": "记忆创建成功",
  "data": {
    "memoryId": "记忆ID",
    "_id": "记忆ID",
    "userId": "用户ID",
    "content": "记忆内容",
    "startDate": "开始日期",
    "endDate": "结束日期",
    "createTime": "创建时间",
    "updateTime": "更新时间"
  },
  "errCode": null,
  "errMsg": "记忆创建成功"
}
```

### 2. getMemories - 获取记忆列表

**参数：**
- `userId` (string, 必需): 用户ID
- `limit` (integer, 可选): 返回数量限制，默认10，最大50
- `offset` (integer, 可选): 偏移量，用于分页，默认0

**返回：**
```json
{
  "success": true,
  "message": "获取记忆列表成功",
  "data": [
    {
      "_id": "记忆ID",
      "userId": "用户ID",
      "content": "记忆内容",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "createTime": "创建时间",
      "updateTime": "更新时间"
    }
  ],
  "errCode": null,
  "errMsg": "获取记忆列表成功"
}
```

### 3. updateMemory - 更新记忆

**参数：**
- `userId` (string, 必需): 用户ID
- `memoryId` (string, 必需): 记忆ID
- `content` (string, 可选): 新的记忆内容
- `startDate` (string, 可选): 新的开始日期
- `endDate` (string, 可选): 新的结束日期

### 4. deleteMemory - 删除记忆

**参数：**
- `userId` (string, 必需): 用户ID
- `memoryId` (string, 必需): 记忆ID

## 使用示例

### 直接调用

```javascript
const MemoryTool = require('./modules/memory')

const memoryTool = new MemoryTool()

// 创建记忆
const result = await memoryTool.execute('createMemory', {
  userId: 'user123',
  content: '我喜欢在早上8点开始工作',
  startDate: '2024-01-01'
})
```

### Function Calling

AI会自动识别用户想要记住信息的意图，并调用相应的记忆工具：

```
用户: "请记住，我每天早上8点开始工作，这是我最有效率的时间"
AI: 调用 createMemory 工具保存此信息

用户: "我之前告诉过你什么偏好？"
AI: 调用 getMemories 工具获取用户记忆
```

## 错误处理

模块定义了完整的错误码体系：

- `PARAM_IS_NULL`: 参数为空
- `CONTENT_TOO_LONG`: 内容过长
- `MEMORY_LIMIT_EXCEEDED`: 记忆数量超限
- `MEMORY_NOT_FOUND`: 记忆不存在
- `DATABASE_ERROR`: 数据库错误
- `UNAUTHORIZED`: 无权限访问

## 数据库Schema

记忆数据存储在 `memory` 集合中，Schema定义位于：
`uniCloud-aliyun/database/memory.schema.json`

## 配置说明

主要配置项（在 `config.js` 中）：

- `MAX_MEMORIES_PER_USER`: 每用户最大记忆数量（100）
- `MEMORY_CONTENT_MAX_LENGTH`: 记忆内容最大长度（2000字符）
- `MEMORY_QUERY_LIMIT`: 单次查询记忆数量限制（50）

## 开发状态

✅ P1阶段已完成：
- [x] 数据库Schema创建
- [x] 记忆工具模块开发
- [x] Function Calling集成
- [x] AI系统集成
- [x] 基础功能验证

## 后续优化

- [ ] 集成uni-id用户认证系统
- [ ] 添加记忆搜索功能
- [ ] 实现记忆分类和标签
- [ ] 添加记忆导入导出功能
- [ ] 优化记忆内容的语义分析

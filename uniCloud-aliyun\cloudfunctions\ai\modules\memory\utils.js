// uniCloud-aliyun/cloudfunctions/ai/modules/memory/utils.js
const {
  MEMORY_CONFIG,
  MEMORY_ERROR_CODES,
  MEMORY_SUCCESS_RESPONSE,
  MEMORY_ERROR_RESPONSE
} = require('./config')

/**
 * 创建成功响应
 * @param {any} data - 响应数据
 * @param {string} message - 成功消息
 * @returns {object} 成功响应对象
 */
function createSuccessResponse(data = null, message = '操作成功') {
  return {
    ...MEMORY_SUCCESS_RESPONSE,
    message,
    data,
    errMsg: message
  }
}

/**
 * 创建错误响应
 * @param {string} errCode - 错误码
 * @param {string} errMsg - 错误消息
 * @param {any} details - 错误详情
 * @returns {object} 错误响应对象
 */
function createErrorResponse(errCode = MEMORY_ERROR_CODES.UNKNOWN_ERROR, errMsg = '操作失败', details = null) {
  return {
    ...MEMORY_ERROR_RESPONSE,
    message: errMsg,
    errCode,
    errMsg,
    details
  }
}

/**
 * 参数校验
 * @param {object} params - 参数对象
 * @param {Array} requiredFields - 必需字段数组
 * @returns {object|null} 错误响应或null
 */
function validateParams(params, requiredFields) {
  for (const field of requiredFields) {
    if (params[field] === undefined || params[field] === null || params[field] === '') {
      return createErrorResponse(
        MEMORY_ERROR_CODES.PARAM_IS_NULL,
        `参数 ${field} 不能为空`
      )
    }
  }
  return null
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 格式化日期时间
 * @param {Date} date - 日期对象
 * @returns {string} ISO格式的日期时间字符串
 */
function formatDateTime(date = new Date()) {
  return date.toISOString()
}

/**
 * 构建包含记忆的系统提示词
 * 在AI对话开始时自动加载用户记忆并融入系统提示词
 */
function buildSystemPromptWithMemories(memories) {
  if (!memories || memories.length === 0) {
    return '你是一个AI助手，帮助用户管理任务。'
  }

  const memoryText = memories
    .map((m, index) => `${index + 1}. ${m.content}`)
    .join('\n')

  return `你是一个AI助手，帮助用户管理任务。

用户之前告诉过你以下信息，请在回答时考虑这些背景：
${memoryText}

请基于这些信息提供个性化的建议和回复。当用户表达想要记住某些信息时，主动调用createMemory工具保存。`
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  generateId,
  formatDateTime,
  buildSystemPromptWithMemories
}

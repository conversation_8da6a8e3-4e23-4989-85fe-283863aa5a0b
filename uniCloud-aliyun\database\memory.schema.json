{"bsonType": "object", "required": ["userId", "content", "createTime", "updateTime"], "permission": {"read": "auth.uid == doc.userId", "create": "auth.uid != null", "update": "auth.uid == doc.userId", "delete": "auth.uid == doc.userId"}, "properties": {"_id": {"description": "记忆ID，系统自动生成"}, "userId": {"bsonType": "string", "description": "用户ID，关联uni-id-users表"}, "content": {"bsonType": "string", "description": "记忆内容，纯文本格式", "maxLength": 2000}, "startDate": {"bsonType": "string", "description": "开始日期，YYYY-MM-DD格式，可选", "pattern": "^\\d{4}-\\d{2}-\\d{2}$"}, "endDate": {"bsonType": "string", "description": "结束日期，YYYY-MM-DD格式，可选", "pattern": "^\\d{4}-\\d{2}-\\d{2}$"}, "createTime": {"bsonType": "string", "description": "创建时间，ISO 8601格式"}, "updateTime": {"bsonType": "string", "description": "更新时间，ISO 8601格式"}}}
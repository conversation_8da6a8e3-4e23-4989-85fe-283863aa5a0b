# AI智能任务系统记忆功能 - 技术方案 - AI对话集成

## 文档说明
本文档专门描述如何让AI在对话时自动获取和应用用户记忆的技术实现，确保AI能够基于用户的历史记忆提供个性化的对话体验。

**开发优先级：P1（高优先级）** - 与记忆工具开发并行进行

**前置条件：**
1. 记忆工具开发完成
2. 记忆工具已通过Function Calling机制注册到AI系统

**核心目标：** 让AI在每次对话开始时自动加载用户记忆，并将记忆融入对话上下文，实现个性化交互。

## AI对话记忆集成实现

### 核心实现原理

记忆功能的核心是在AI对话开始时自动加载用户的历史记忆，并将这些记忆融入到AI的系统提示词中，让AI能够基于用户的个人背景和偏好提供个性化的回复。

**关键技术点：**
1. **自动记忆加载**：每次对话开始时自动从数据库加载用户记忆
2. **系统提示词增强**：将记忆内容融入AI的系统提示词
3. **透明化处理**：用户无感知，但体验个性化
4. **Function Calling集成**：AI根据对话内容自动调用记忆相关工具

### chatStreamSSE记忆集成实现

**集成原则**：
- 记忆功能作为增强功能，不影响现有对话流程
- 记忆加载失败时自动降级到默认行为
- 采用简单直接的集成方式，保持代码清晰

```javascript
// uniCloud-aliyun/cloudfunctions/ai/index.obj.js 中的 chatStreamSSE 修改

async chatStreamSSE({ message, messages, channel }) {
  const userId = this.getUniIdToken()?.uid

  try {
    // 导入记忆工具
    const { buildSystemPromptWithMemories } = require('./modules/memory/utils')
    const MemoryTool = require('./modules/memory')

    // 1. 自动加载用户记忆并构建系统提示词
    let systemPrompt = '你是一个AI助手，帮助用户管理任务。'

    if (userId) {
      const memoryTool = new MemoryTool()
      // 使用记忆工具的execute方法
      const memoriesResult = await memoryTool.execute('getMemories', {
        userId,
        limit: 10  // 加载最近10条记忆，避免提示词过长
      })

      // 检查结果格式
      if (memoriesResult.success || (!memoriesResult.errCode && memoriesResult.data)) {
        const memories = memoriesResult.data
        if (memories && memories.length > 0) {
          systemPrompt = buildSystemPromptWithMemories(memories)
          console.log(`[chatStreamSSE] 已加载 ${memories.length} 条用户记忆`)
        }
      }
    }

    // 2. 使用包含记忆的系统提示词进行AI对话
    // AI会根据用户意图自动调用记忆相关的Function Calling工具
    // 例如：用户说"记住我喜欢Vue.js" → AI自动调用createMemory工具
    // 例如：用户说"帮我创建学习任务" → AI基于记忆中的学习偏好提供建议

    // ... 原有的AI对话逻辑，使用修改后的systemPrompt
    // 注意：这里需要将systemPrompt传递给AI模型

  } catch (error) {
    console.error('[chatStreamSSE] 记忆功能错误:', error)
    // 记忆功能出错时不影响正常对话，使用默认系统提示词
    // 确保系统的健壮性
  }
}
```

**集成要点**：
1. **简单调用**：使用 `memoryTool.execute()` 方法
2. **响应格式兼容**：支持记忆工具的响应格式，确保兼容性
3. **错误处理**：记忆功能异常不影响正常对话功能
4. **性能考虑**：限制记忆加载数量，避免系统提示词过长

## 记忆融入对话的工作流程

### 1. 对话启动阶段
```
用户发起对话 → chatStreamSSE启动 → 自动加载用户记忆 → 构建增强系统提示词
```

### 2. 记忆应用阶段
```
AI基于记忆背景理解用户意图 → 提供个性化回复 → 根据需要调用Function Calling工具
```

### 3. 记忆更新阶段
```
用户表达记忆需求 → AI识别意图 → 自动调用createMemory工具 → 保存新记忆
```

## 系统提示词增强机制

### buildSystemPromptWithMemories 函数详解

```javascript
// modules/memory/utils.js 中的核心函数

/**
 * 构建包含记忆的系统提示词
 * 将用户的历史记忆融入AI的系统提示词中
 */
function buildSystemPromptWithMemories(memories) {
  if (!memories || memories.length === 0) {
    return '你是一个AI助手，帮助用户管理任务。'
  }

  // 将记忆内容格式化为文本
  const memoryText = memories
    .map((memory, index) => `${index + 1}. ${memory.content}`)
    .join('\n')

  // 构建增强的系统提示词
  return `你是一个AI助手，帮助用户管理任务。

用户之前告诉过你以下信息，请在回答时考虑这些背景：
${memoryText}

请基于这些信息提供个性化的建议和回复。当用户表达想要记住某些信息时，主动调用createMemory工具保存。`
}
```

### 记忆加载策略

**加载时机：** 每次AI对话开始时自动加载
**加载数量：** 限制为10条最新记忆，避免系统提示词过长
**失败处理：** 记忆加载失败时不影响正常对话，使用默认提示词
**性能考虑：** 异步加载，不阻塞对话响应

## 实际应用场景示例

### 场景1：个性化任务创建
```
用户记忆：["我在学习Vue.js", "我喜欢在周末学习", "我偏好使用番茄工作法"]

用户输入："帮我创建一个学习任务"
AI处理：基于记忆背景 → 理解用户偏好 → 调用createTask工具
AI回复："根据您学习Vue.js的目标，我为您创建了一个周末的Vue.js学习任务，建议使用番茄工作法进行。"
```

### 场景2：记忆自动保存
```
用户输入："我最近在准备面试，主要关注React和Node.js"
AI理解：用户想要记住重要信息 → 自动调用createMemory工具
AI回复："好的，我已经记住您在准备面试，关注React和Node.js。祝您面试顺利！"
```

### 场景3：基于记忆的建议
```
用户记忆：["正在准备面试，关注React和Node.js"]

用户输入："推荐一些学习资源"
AI处理：基于记忆了解用户需求 → 提供针对性建议
AI回复："基于您的面试准备需求，我推荐以下React和Node.js的学习资源..."
```

## P1阶段集成实施计划

### 集成任务（与记忆工具开发并行）

**任务1：记忆工具函数实现** (0.2天)
- 实现 `memory/utils.js` 中的 `buildSystemPromptWithMemories` 函数
- 确保与现有系统提示词格式兼容

**任务2：chatStreamSSE集成** (0.3天)
- 修改 `ai/index.obj.js` 中的 `chatStreamSSE` 方法
- 添加记忆自动加载逻辑
- 确保错误处理和降级机制

**任务3：集成测试** (0.2天)
- 测试记忆加载和应用功能
- 验证Function Calling工具调用
- 确保现有对话功能不受影响

### 集成验证清单

**功能验证**：
- [ ] 记忆自动加载功能正常
- [ ] 系统提示词增强功能正常
- [ ] AI基于记忆提供个性化回复
- [ ] Function Calling工具调用正常

**兼容性验证**：
- [ ] 现有对话功能不受影响
- [ ] 记忆功能异常时正常降级
- [ ] 响应格式完全兼容

## 技术要点总结

### 关键实现步骤
1. **在chatStreamSSE启动时自动加载用户记忆**
2. **使用buildSystemPromptWithMemories构建增强提示词**
3. **将增强提示词传递给AI模型**
4. **AI基于记忆背景进行个性化回复**
5. **AI根据对话内容自动调用记忆相关Function Calling工具**

### 核心优势
- **无感知集成**：用户无需学习新的操作方式
- **自动化处理**：AI自主判断何时使用和更新记忆
- **个性化体验**：基于用户历史信息提供定制化服务
- **健壮性强**：记忆功能异常不影响核心对话功能

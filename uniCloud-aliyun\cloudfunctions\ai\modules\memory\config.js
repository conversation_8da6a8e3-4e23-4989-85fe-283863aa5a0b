// uniCloud-aliyun/cloudfunctions/ai/modules/memory/config.js

// 记忆模块错误码
const MEMORY_ERROR_CODES = {
  // 通用错误码
  PARAM_IS_NULL: 'PARAM_IS_NULL',
  PARAM_INVALID: 'PARAM_INVALID',
  UNAUTHORIZED: 'UNAUTHORIZED',
  DATABASE_ERROR: 'DATABASE_ERROR',
  DATA_NOT_FOUND: 'DATA_NOT_FOUND',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  OPERATION_FAILED: 'OPERATION_FAILED',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',

  // 记忆特有错误码
  MEMORY_NOT_FOUND: 'MEMORY_NOT_FOUND',           // 记忆不存在
  CONTENT_TOO_LONG: 'CONTENT_TOO_LONG',           // 内容过长
  MEMORY_LIMIT_EXCEEDED: 'MEMORY_LIMIT_EXCEEDED'  // 记忆数量超限
}

// 记忆模块配置
const MEMORY_CONFIG = {
  // 记忆业务配置
  MAX_MEMORIES_PER_USER: 100,        // 每用户最大记忆数量
  MEMORY_QUERY_LIMIT: 50,            // 单次查询记忆数量限制
  MEMORY_CONTENT_MAX_LENGTH: 2000,   // 记忆内容最大长度

  // 数据库查询配置
  DEFAULT_QUERY_LIMIT: 10,           // 默认查询数量
  MAX_QUERY_LIMIT: 50,               // 最大查询数量

  // 日期时间格式
  DATE_FORMAT: 'YYYY-MM-DD',
  DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss'
}

// 记忆模块响应模板
const MEMORY_SUCCESS_RESPONSE = {
  success: true,
  message: '操作成功',
  data: null,
  errCode: null,
  errMsg: '操作成功'
}

const MEMORY_ERROR_RESPONSE = {
  success: false,
  message: '操作失败',
  data: null,
  errCode: 'UNKNOWN_ERROR',
  errMsg: '操作失败',
  details: null
}

module.exports = {
  MEMORY_CONFIG,
  MEMORY_ERROR_CODES,
  MEMORY_SUCCESS_RESPONSE,
  MEMORY_ERROR_RESPONSE
}

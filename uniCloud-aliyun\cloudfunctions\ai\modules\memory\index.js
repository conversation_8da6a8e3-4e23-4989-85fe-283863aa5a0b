// uniCloud-aliyun/cloudfunctions/ai/modules/memory/index.js
const MemoryManager = require('./memories')
const { MEMORY_ERROR_CODES } = require('./config')
const { createErrorResponse } = require('./utils')

/**
 * 记忆工具类
 */
class MemoryTool {
  constructor() {
    this.toolName = 'MemoryTool'
    this.memoryManager = new MemoryManager()
    console.log(`[${this.toolName}] 工具初始化完成`)
  }

  /**
   * 工具执行入口
   * @param {string} method - 方法名
   * @param {object} parameters - 参数对象
   * @returns {object} 执行结果
   */
  async execute(method, parameters = {}) {
    const executeStartTime = Date.now()

    console.log(`[${this.toolName}] [execute] 方法开始：${method}`)
    console.log(`[${this.toolName}] [execute] 输入参数`, { method, parameters })

    try {
      let result

      // 使用switch-case执行模式
      switch (method) {
        case 'createMemory':
          result = await this.memoryManager.createMemory(parameters)
          break
        case 'getMemories':
          result = await this.memoryManager.getMemories(parameters)
          break
        case 'updateMemory':
          result = await this.memoryManager.updateMemory(parameters)
          break
        case 'deleteMemory':
          result = await this.memoryManager.deleteMemory(parameters)
          break
        default:
          const error = new Error(`未知的方法：${method}`)
          console.error(`[${this.toolName}] [execute] 未知方法`, {
            method,
            availableMethods: ['createMemory', 'getMemories', 'updateMemory', 'deleteMemory'],
            error: error.message
          })
          throw error
      }

      const executeEndTime = Date.now()
      console.log(`[${this.toolName}] [execute] 方法执行成功：${method}`, {
        duration: executeEndTime - executeStartTime,
        resultStatus: result?.errCode === null || result?.errCode === 0 ? 'success' : 'error'
      })

      return result

    } catch (error) {
      const executeEndTime = Date.now()
      console.error(`[${this.toolName}] [execute] 方法执行异常：${method}`, {
        error: error.message,
        duration: executeEndTime - executeStartTime
      })

      return createErrorResponse(MEMORY_ERROR_CODES.UNKNOWN_ERROR, error.message, error)
    }
  }
}

module.exports = MemoryTool

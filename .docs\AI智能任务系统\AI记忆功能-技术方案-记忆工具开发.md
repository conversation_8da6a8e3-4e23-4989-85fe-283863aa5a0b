# AI智能任务系统记忆功能 - 技术方案 - 记忆工具开发

## 文档说明
本文档专门描述记忆工具的开发和具体实现内容。

**开发优先级：P1（最高优先级）**

**前置条件：** 需求文档确认

**依赖关系：** 独立开发，不依赖其他模块。

## 数据库表结构定义

### 记忆数据表 Schema (uniCloud-aliyun/database/memory.schema.json)

```json
{
  "bsonType": "object",
  "required": ["userId", "content", "createTime", "updateTime"],
  "permission": {
    "read": "auth.uid == doc.userId",
    "create": "auth.uid != null",
    "update": "auth.uid == doc.userId",
    "delete": "auth.uid == doc.userId"
  },
  "properties": {
    "_id": {
      "description": "记忆ID，系统自动生成"
    },
    "userId": {
      "bsonType": "string",
      "description": "用户ID，关联uni-id-users表"
    },
    "content": {
      "bsonType": "string",
      "description": "记忆内容，纯文本格式",
      "maxLength": 2000
    },
    "startDate": {
      "bsonType": "string",
      "description": "开始日期，YYYY-MM-DD格式，可选",
      "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
    },
    "endDate": {
      "bsonType": "string",
      "description": "结束日期，YYYY-MM-DD格式，可选",
      "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
    },
    "createTime": {
      "bsonType": "string",
      "description": "创建时间，ISO 8601格式"
    },
    "updateTime": {
      "bsonType": "string",
      "description": "更新时间，ISO 8601格式"
    }
  }
}
```

## 记忆模块架构

### 模块结构
```
uniCloud-aliyun/cloudfunctions/ai/modules/memory/
├── index.js             # 记忆工具主入口
├── config.js            # 记忆模块配置
├── memories.js          # 记忆管理核心逻辑
└── utils.js             # 记忆工具函数
```

## 记忆模块配置 (memory/config.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/config.js

// 记忆模块错误码
const MEMORY_ERROR_CODES = {
  // 通用错误码
  PARAM_IS_NULL: 'PARAM_IS_NULL',
  PARAM_INVALID: 'PARAM_INVALID',
  UNAUTHORIZED: 'UNAUTHORIZED',
  DATABASE_ERROR: 'DATABASE_ERROR',
  DATA_NOT_FOUND: 'DATA_NOT_FOUND',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  OPERATION_FAILED: 'OPERATION_FAILED',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',

  // 记忆特有错误码
  MEMORY_NOT_FOUND: 'MEMORY_NOT_FOUND',           // 记忆不存在
  CONTENT_TOO_LONG: 'CONTENT_TOO_LONG',           // 内容过长
  MEMORY_LIMIT_EXCEEDED: 'MEMORY_LIMIT_EXCEEDED'  // 记忆数量超限
}

// 记忆模块配置
const MEMORY_CONFIG = {
  // 记忆业务配置
  MAX_MEMORIES_PER_USER: 100,        // 每用户最大记忆数量
  MEMORY_QUERY_LIMIT: 50,            // 单次查询记忆数量限制
  MEMORY_CONTENT_MAX_LENGTH: 2000,   // 记忆内容最大长度

  // 数据库查询配置
  DEFAULT_QUERY_LIMIT: 10,           // 默认查询数量
  MAX_QUERY_LIMIT: 50,               // 最大查询数量

  // 日期时间格式
  DATE_FORMAT: 'YYYY-MM-DD',
  DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss'
}

// 记忆模块响应模板
const MEMORY_SUCCESS_RESPONSE = {
  success: true,
  message: '操作成功',
  data: null,
  errCode: null,
  errMsg: '操作成功'
}

const MEMORY_ERROR_RESPONSE = {
  success: false,
  message: '操作失败',
  data: null,
  errCode: 'UNKNOWN_ERROR',
  errMsg: '操作失败',
  details: null
}

module.exports = {
  MEMORY_CONFIG,
  MEMORY_ERROR_CODES,
  MEMORY_SUCCESS_RESPONSE,
  MEMORY_ERROR_RESPONSE
}
```



## 记忆模块工具函数 (memory/utils.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/utils.js
const {
  MEMORY_CONFIG,
  MEMORY_ERROR_CODES,
  MEMORY_SUCCESS_RESPONSE,
  MEMORY_ERROR_RESPONSE
} = require('./config')

/**
 * 创建成功响应
 * @param {any} data - 响应数据
 * @param {string} message - 成功消息
 * @returns {object} 成功响应对象
 */
function createSuccessResponse(data = null, message = '操作成功') {
  return {
    ...MEMORY_SUCCESS_RESPONSE,
    message,
    data,
    errMsg: message
  }
}

/**
 * 创建错误响应
 * @param {string} errCode - 错误码
 * @param {string} errMsg - 错误消息
 * @param {any} details - 错误详情
 * @returns {object} 错误响应对象
 */
function createErrorResponse(errCode = MEMORY_ERROR_CODES.UNKNOWN_ERROR, errMsg = '操作失败', details = null) {
  return {
    ...MEMORY_ERROR_RESPONSE,
    message: errMsg,
    errCode,
    errMsg,
    details
  }
}

/**
 * 参数校验
 * @param {object} params - 参数对象
 * @param {Array} requiredFields - 必需字段数组
 * @returns {object|null} 错误响应或null
 */
function validateParams(params, requiredFields) {
  for (const field of requiredFields) {
    if (params[field] === undefined || params[field] === null || params[field] === '') {
      return createErrorResponse(
        MEMORY_ERROR_CODES.PARAM_IS_NULL,
        `参数 ${field} 不能为空`
      )
    }
  }
  return null
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 格式化日期时间
 * @param {Date} date - 日期对象
 * @returns {string} ISO格式的日期时间字符串
 */
function formatDateTime(date = new Date()) {
  return date.toISOString()
}

/**
 * 构建包含记忆的系统提示词
 * 在AI对话开始时自动加载用户记忆并融入系统提示词
 */
function buildSystemPromptWithMemories(memories) {
  if (!memories || memories.length === 0) {
    return '你是一个AI助手，帮助用户管理任务。'
  }

  const memoryText = memories
    .map((m, index) => `${index + 1}. ${m.content}`)
    .join('\n')

  return `你是一个AI助手，帮助用户管理任务。

用户之前告诉过你以下信息，请在回答时考虑这些背景：
${memoryText}

请基于这些信息提供个性化的建议和回复。当用户表达想要记住某些信息时，主动调用createMemory工具保存。`
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  generateId,
  formatDateTime,
  buildSystemPromptWithMemories
}
```

## 记忆管理核心类 (memory/memories.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/memories.js
const { MEMORY_CONFIG, MEMORY_ERROR_CODES } = require('./config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  generateId,
  formatDateTime
} = require('./utils')

/**
 * 记忆管理类
 */
class MemoryManager {
  constructor() {
    this.db = uniCloud.database()
    this.collection = this.db.collection('memory')
  }

  /**
   * 创建记忆
   */
  async createMemory(options = {}) {
    const { userId, content, startDate = null, endDate = null } = options

    // 参数校验
    const validation = validateParams({ userId, content }, ['userId', 'content'])
    if (validation) return validation

    // 内容长度校验
    if (content.length > MEMORY_CONFIG.MEMORY_CONTENT_MAX_LENGTH) {
      return createErrorResponse(
        MEMORY_ERROR_CODES.CONTENT_TOO_LONG,
        `记忆内容不能超过${MEMORY_CONFIG.MEMORY_CONTENT_MAX_LENGTH}个字符`
      )
    }

    try {
      // 检查用户记忆数量限制
      const { total } = await this.collection.where({ userId }).count()
      if (total >= MEMORY_CONFIG.MAX_MEMORIES_PER_USER) {
        return createErrorResponse(
          MEMORY_ERROR_CODES.MEMORY_LIMIT_EXCEEDED,
          `每个用户最多只能保存${MEMORY_CONFIG.MAX_MEMORIES_PER_USER}条记忆`
        )
      }

      const now = formatDateTime()
      const memoryData = {
        _id: generateId(),
        userId,
        content: content.trim(),
        startDate,
        endDate,
        createTime: now,
        updateTime: now
      }

      const result = await this.collection.add(memoryData)

      return createSuccessResponse({
        memoryId: result.id,
        ...memoryData
      }, '记忆创建成功')

    } catch (error) {
      console.error('[MemoryManager.createMemory] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }

  /**
   * 获取记忆列表
   */
  async getMemories(options = {}) {
    const { userId, limit = MEMORY_CONFIG.MEMORY_QUERY_LIMIT, offset = 0 } = options

    // 参数校验
    const validation = validateParams({ userId }, ['userId'])
    if (validation) return validation

    try {
      const query = this.collection
        .where({ userId })
        .orderBy('createTime', 'desc')
        .skip(offset)
        .limit(Math.min(limit, MEMORY_CONFIG.MAX_QUERY_LIMIT))

      const result = await query.get()

      return createSuccessResponse(result.data, '获取记忆列表成功')

    } catch (error) {
      console.error('[MemoryManager.getMemories] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }

  /**
   * 更新记忆
   */
  async updateMemory(options = {}) {
    const { userId, memoryId, content, startDate, endDate } = options

    // 参数校验
    const validation = validateParams({ userId, memoryId }, ['userId', 'memoryId'])
    if (validation) return validation

    try {
      const updateData = {
        updateTime: formatDateTime()
      }

      if (content !== undefined) {
        if (content.length > MEMORY_CONFIG.MEMORY_CONTENT_MAX_LENGTH) {
          return createErrorResponse(
            MEMORY_ERROR_CODES.CONTENT_TOO_LONG,
            `记忆内容不能超过${MEMORY_CONFIG.MEMORY_CONTENT_MAX_LENGTH}个字符`
          )
        }
        updateData.content = content.trim()
      }

      if (startDate !== undefined) updateData.startDate = startDate
      if (endDate !== undefined) updateData.endDate = endDate

      const result = await this.collection
        .where({ _id: memoryId, userId })
        .update(updateData)

      if (result.updated === 0) {
        return createErrorResponse(MEMORY_ERROR_CODES.MEMORY_NOT_FOUND, '记忆不存在或无权限访问')
      }

      return createSuccessResponse({ memoryId, ...updateData }, '记忆更新成功')

    } catch (error) {
      console.error('[MemoryManager.updateMemory] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }

  /**
   * 删除记忆
   */
  async deleteMemory(options = {}) {
    const { userId, memoryId } = options

    // 参数校验
    const validation = validateParams({ userId, memoryId }, ['userId', 'memoryId'])
    if (validation) return validation

    try {
      const result = await this.collection
        .where({ _id: memoryId, userId })
        .remove()

      if (result.deleted === 0) {
        return createErrorResponse(MEMORY_ERROR_CODES.MEMORY_NOT_FOUND, '记忆不存在或无权限访问')
      }

      return createSuccessResponse({ memoryId }, '记忆删除成功')

    } catch (error) {
      console.error('[MemoryManager.deleteMemory] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }
}

module.exports = MemoryManager
```

## 记忆工具主入口 (memory/index.js)

```javascript
// uniCloud-aliyun/cloudfunctions/ai/modules/memory/index.js
const MemoryManager = require('./memories')
const { MEMORY_ERROR_CODES } = require('./config')
const { createErrorResponse } = require('./utils')

/**
 * 记忆工具类
 */
class MemoryTool {
  constructor() {
    this.toolName = 'MemoryTool'
    this.memoryManager = new MemoryManager()
    console.log(`[${this.toolName}] 工具初始化完成`)
  }

  /**
   * 工具执行入口
   * @param {string} method - 方法名
   * @param {object} parameters - 参数对象
   * @returns {object} 执行结果
   */
  async execute(method, parameters = {}) {
    const executeStartTime = Date.now()

    console.log(`[${this.toolName}] [execute] 方法开始：${method}`)
    console.log(`[${this.toolName}] [execute] 输入参数`, { method, parameters })

    try {
      let result

      // 使用switch-case执行模式
      switch (method) {
        case 'createMemory':
          result = await this.memoryManager.createMemory(parameters)
          break
        case 'getMemories':
          result = await this.memoryManager.getMemories(parameters)
          break
        case 'updateMemory':
          result = await this.memoryManager.updateMemory(parameters)
          break
        case 'deleteMemory':
          result = await this.memoryManager.deleteMemory(parameters)
          break
        default:
          const error = new Error(`未知的方法：${method}`)
          console.error(`[${this.toolName}] [execute] 未知方法`, {
            method,
            availableMethods: ['createMemory', 'getMemories', 'updateMemory', 'deleteMemory'],
            error: error.message
          })
          throw error
      }

      const executeEndTime = Date.now()
      console.log(`[${this.toolName}] [execute] 方法执行成功：${method}`, {
        duration: executeEndTime - executeStartTime,
        resultStatus: result?.errCode === null || result?.errCode === 0 ? 'success' : 'error'
      })

      return result

    } catch (error) {
      const executeEndTime = Date.now()
      console.error(`[${this.toolName}] [execute] 方法执行异常：${method}`, {
        error: error.message,
        duration: executeEndTime - executeStartTime
      })

      return createErrorResponse(MEMORY_ERROR_CODES.UNKNOWN_ERROR, error.message, error)
    }
  }
}

module.exports = MemoryTool
```

## Function Calling 工具注册

记忆工具需要注册到AI的Function Calling系统中，在 `config.js` 的 `FUNCTION_TOOLS` 数组中添加：

```javascript
// 在 uniCloud-aliyun/cloudfunctions/ai/modules/config.js 的 FUNCTION_TOOLS 数组中添加

// === 记忆管理工具 ===
{
  type: 'function',
  function: {
    name: 'createMemory',
    description: '保存用户想要记住的重要信息，用于后续对话中提供个性化服务',
    parameters: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: '要记住的内容，用户想要AI记住的重要信息'
        },
        startDate: {
          type: 'string',
          description: '记忆的开始日期，格式：YYYY-MM-DD，可选'
        },
        endDate: {
          type: 'string',
          description: '记忆的结束日期，格式：YYYY-MM-DD，可选'
        }
      },
      required: ['content']
    }
  }
},
{
  type: 'function',
  function: {
    name: 'getMemories',
    description: '获取用户之前保存的记忆信息，用于了解用户背景和偏好',
    parameters: {
      type: 'object',
      properties: {
        limit: {
          type: 'integer',
          description: '返回记忆的数量限制，默认10',
          minimum: 1,
          maximum: 50
        },
        offset: {
          type: 'integer',
          description: '偏移量，用于分页，默认0',
          minimum: 0
        }
      }
    }
  }
},
{
  type: 'function',
  function: {
    name: 'updateMemory',
    description: '更新已保存的记忆内容',
    parameters: {
      type: 'object',
      properties: {
        memoryId: {
          type: 'string',
          description: '要更新的记忆ID'
        },
        content: {
          type: 'string',
          description: '新的记忆内容'
        },
        startDate: {
          type: 'string',
          description: '新的开始日期，格式：YYYY-MM-DD，可选'
        },
        endDate: {
          type: 'string',
          description: '新的结束日期，格式：YYYY-MM-DD，可选'
        }
      },
      required: ['memoryId']
    }
  }
},
{
  type: 'function',
  function: {
    name: 'deleteMemory',
    description: '删除不再需要的记忆',
    parameters: {
      type: 'object',
      properties: {
        memoryId: {
          type: 'string',
          description: '要删除的记忆ID'
        }
      },
      required: ['memoryId']
    }
  }
}
```

## 实施计划

### P1阶段：记忆功能开发 (2天)

**开发任务**：

1. **数据库Schema创建** (0.5天)
   - 创建 `uniCloud-aliyun/database/memory.schema.json`
   - 配置权限和字段验证规则
   - 验证数据库连接和基本操作

2. **记忆工具模块开发** (1天)
   - 创建 `modules/memory/` 文件夹结构
   - 实现 `memory/config.js`：记忆模块配置
   - 实现 `memory/utils.js`：记忆工具函数
   - 实现 `memory/memories.js`：记忆管理核心逻辑
   - 实现 `memory/index.js`：记忆工具主入口

3. **Function Calling集成** (0.5天)
   - 在 `modules/config.js` 的 `FUNCTION_TOOLS` 数组中注册记忆工具
   - 在 `ai/index.obj.js` 中集成MemoryTool
   - 验证Function Calling调用正常

**完成标准**：
- 记忆工具实现完成，功能完整
- 所有记忆CRUD操作功能正常
- Function Calling集成测试通过

### P1阶段验证清单

**数据库层验证**：
- [ ] 记忆数据库Schema创建完成
- [ ] 数据库权限配置正确
- [ ] 基本CRUD操作测试通过

**模块层验证**：
- [ ] 记忆配置模块实现完成
- [ ] 记忆工具函数模块实现完成
- [ ] 记忆管理核心类实现完成
- [ ] 记忆工具主入口实现完成

**集成层验证**：
- [ ] Function Calling工具注册完成
- [ ] AI系统集成完成
- [ ] 记忆CRUD操作通过Function Calling测试通过

**功能验证**：
- [ ] 记忆创建功能正常
- [ ] 记忆查询功能正常
- [ ] 记忆更新功能正常
- [ ] 记忆删除功能正常
- [ ] 系统提示词增强功能正常
- [ ] AI个性化对话功能正常


